"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Shield, 
  User, 
  CheckCircle, 
  AlertTriangle,
  Loader2,
  ExternalLink,
  Database,
  Key,
  Users,
  Crown
} from "lucide-react"
import { createAuthTables } from "@/lib/auth-supabase"

export default function TestAuthPage() {
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState<string[]>([])
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const addResult = (message: string) => {
    setResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  const testDatabaseSetup = async () => {
    try {
      setLoading(true)
      setError(null)
      setResults([])
      
      addResult('🔧 Création des tables d\'authentification...')
      
      const { success, error } = await createAuthTables()
      
      if (success) {
        addResult('✅ Tables créées avec succès')
        addResult('✅ Utilisateurs admin ajoutés')
        addResult('📋 Comptes disponibles:')
        addResult('   - admin / admin123')
        addResult('   - taxhaa / royaume2024')
        setSuccess('Configuration de la base de données terminée !')
      } else {
        addResult(`❌ Erreur: ${error}`)
        setError('Erreur lors de la configuration')
      }
    } catch (err) {
      addResult(`❌ Erreur inattendue: ${err}`)
      setError('Erreur inattendue')
    } finally {
      setLoading(false)
    }
  }

  const testDiscordConfig = () => {
    addResult('🔍 Vérification de la configuration Discord...')
    
    const clientId = process.env.NEXT_PUBLIC_DISCORD_CLIENT_ID
    const hasClientSecret = !!process.env.DISCORD_CLIENT_SECRET
    
    if (clientId) {
      addResult(`✅ Discord Client ID: ${clientId}`)
    } else {
      addResult('❌ Discord Client ID manquant')
    }
    
    if (hasClientSecret) {
      addResult('✅ Discord Client Secret configuré')
    } else {
      addResult('❌ Discord Client Secret manquant')
    }
    
    if (mounted) {
      const redirectUri = `${window.location.origin}/auth/discord/callback`
      addResult(`📍 Redirect URI: ${redirectUri}`)
      addResult('⚠️  Assurez-vous que cette URL est configurée dans Discord Developer Portal')
    }
    
    if (clientId && hasClientSecret) {
      setSuccess('Configuration Discord OK !')
    } else {
      setError('Configuration Discord incomplète')
    }
  }

  const openDiscordDeveloperPortal = () => {
    if (mounted) {
      window.open('https://discord.com/developers/applications', '_blank')
    }
  }

  const testDiscordOAuth = () => {
    const clientId = process.env.NEXT_PUBLIC_DISCORD_CLIENT_ID
    if (!clientId) {
      setError('Client ID Discord manquant')
      return
    }
    
    if (mounted) {
      const redirectUri = encodeURIComponent(`${window.location.origin}/auth/discord/callback`)
      const discordAuthUrl = `https://discord.com/api/oauth2/authorize?client_id=${clientId}&redirect_uri=${redirectUri}&response_type=code&scope=identify%20email`

      addResult('🔗 Redirection vers Discord OAuth...')
      window.open(discordAuthUrl, '_blank')
    }
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <h1 className="text-3xl font-bold mb-6 flex items-center gap-2">
        <Shield className="h-8 w-8 text-primary" />
        Test d'Authentification
      </h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Configuration de la base de données */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Configuration Base de Données
              </CardTitle>
              <CardDescription>
                Créer les tables et utilisateurs admin
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button 
                onClick={testDatabaseSetup} 
                disabled={loading}
                className="w-full"
              >
                {loading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Configuration...
                  </>
                ) : (
                  <>
                    <Database className="h-4 w-4 mr-2" />
                    Configurer la Base de Données
                  </>
                )}
              </Button>
              
              <div className="space-y-2">
                <h4 className="font-medium">Tables créées :</h4>
                <ul className="text-sm space-y-1">
                  <li>• <code>admin_users</code> - Comptes administrateurs</li>
                  <li>• <code>users</code> - Utilisateurs Discord</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="h-5 w-5" />
                Configuration Discord
              </CardTitle>
              <CardDescription>
                Vérifier la configuration OAuth Discord
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button 
                onClick={testDiscordConfig}
                variant="outline"
                className="w-full"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Vérifier Configuration
              </Button>
              
              <Button 
                onClick={openDiscordDeveloperPortal}
                variant="outline"
                className="w-full"
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Discord Developer Portal
              </Button>
              
              <div className="space-y-2">
                <h4 className="font-medium">Configuration requise :</h4>
                <ul className="text-sm space-y-1">
                  <li>• Client ID dans .env.local</li>
                  <li>• Client Secret dans .env.local</li>
                  <li>• Redirect URI configurée</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Test OAuth Discord
              </CardTitle>
              <CardDescription>
                Tester la connexion Discord
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button 
                onClick={testDiscordOAuth}
                className="w-full"
              >
                <svg className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515a.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0a12.64 12.64 0 0 0-.617-1.25a.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057a19.9 19.9 0 0 0 5.993 3.03a.078.078 0 0 0 .084-.028a14.09 14.09 0 0 0 1.226-1.994a.076.076 0 0 0-.041-.106a13.107 13.107 0 0 1-1.872-.892a.077.077 0 0 1-.008-.128a10.2 10.2 0 0 0 .372-.292a.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.**************.373.292a.077.077 0 0 1-.006.127a12.299 12.299 0 0 1-1.873.892a.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028a19.839 19.839 0 0 0 6.002-3.03a.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419c0-1.333.956-2.419 2.157-2.419c1.21 0 2.176 1.096 2.157 2.42c0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419c0-1.333.955-2.419 2.157-2.419c1.21 0 2.176 1.096 2.157 2.42c0 1.333-.946 2.418-2.157 2.418z"/>
                </svg>
                Tester Discord OAuth
              </Button>
              
              <div className="text-sm text-muted-foreground">
                Cela ouvrira Discord dans un nouvel onglet pour tester l'authentification
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Résultats et logs */}
        <div className="space-y-6">
          {/* Messages */}
          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>{success}</AlertDescription>
            </Alert>
          )}

          {/* Console de résultats */}
          <Card>
            <CardHeader>
              <CardTitle>Résultats des Tests</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-900 text-green-400 p-4 rounded font-mono text-sm h-64 overflow-y-auto">
                {results.length === 0 ? (
                  <div className="text-gray-500">Aucun test exécuté. Cliquez sur un bouton pour commencer.</div>
                ) : (
                  results.map((result, index) => (
                    <div key={index} className="mb-1">
                      {result}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>

          {/* Actions rapides */}
          <Card>
            <CardHeader>
              <CardTitle>Actions Rapides</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" className="w-full" asChild>
                <a href="/auth/login">
                  <Crown className="h-4 w-4 mr-2" />
                  Page de Connexion
                </a>
              </Button>
              <Button variant="outline" className="w-full" asChild>
                <a href="/admin">
                  <Shield className="h-4 w-4 mr-2" />
                  Panneau Admin
                </a>
              </Button>
              <Button variant="outline" className="w-full" asChild>
                <a href="/test-db">
                  <Database className="h-4 w-4 mr-2" />
                  Test Base de Données
                </a>
              </Button>
            </CardContent>
          </Card>

          {/* Informations */}
          <Card>
            <CardHeader>
              <CardTitle>Informations</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm">
              <div>
                <strong>Client ID Discord:</strong> 
                <Badge variant="outline" className="ml-2">
                  {process.env.NEXT_PUBLIC_DISCORD_CLIENT_ID || 'Non configuré'}
                </Badge>
              </div>
              <div>
                <strong>Redirect URI:</strong>
                <code className="ml-2 text-xs bg-gray-100 px-2 py-1 rounded">
                  {mounted ? `${window.location.origin}/auth/discord/callback` : 'Chargement...'}
                </code>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
