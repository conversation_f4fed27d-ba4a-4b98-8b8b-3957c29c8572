"use client"

import { useState, useEffect, createContext, useContext, ReactNode } from 'react'
import { User, loginAdmin, loginWithDiscord, getUser, AdminCredentials, DiscordUser } from '@/lib/auth-supabase'

interface AuthContextType {
  user: User | null
  loading: boolean
  error: string | null
  login: (credentials: AdminCredentials) => Promise<boolean>
  loginDiscord: (discordUser: DiscordUser) => Promise<boolean>
  logout: () => void
  isAdmin: boolean
  isAuthenticated: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export function useAuthProvider() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Charger l'utilisateur depuis le localStorage au démarrage
  useEffect(() => {
    const loadUser = async () => {
      try {
        const savedUserId = localStorage.getItem('auth_user_id')
        if (savedUserId) {
          const userData = await getUser(savedUserId)
          if (userData) {
            setUser(userData)
          } else {
            localStorage.removeItem('auth_user_id')
          }
        }
      } catch (error) {
        console.error('Erreur lors du chargement de l\'utilisateur:', error)
        localStorage.removeItem('auth_user_id')
      } finally {
        setLoading(false)
      }
    }

    loadUser()
  }, [])

  const login = async (credentials: AdminCredentials): Promise<boolean> => {
    try {
      setLoading(true)
      setError(null)

      const { user: userData, error: loginError } = await loginAdmin(credentials)
      
      if (loginError || !userData) {
        setError(loginError || 'Erreur de connexion')
        return false
      }

      setUser(userData)
      localStorage.setItem('auth_user_id', userData.id)
      return true
    } catch (error) {
      setError('Erreur de connexion')
      return false
    } finally {
      setLoading(false)
    }
  }

  const loginDiscord = async (discordUser: DiscordUser): Promise<boolean> => {
    try {
      setLoading(true)
      setError(null)

      const { user: userData, error: loginError } = await loginWithDiscord(discordUser)
      
      if (loginError || !userData) {
        setError(loginError || 'Erreur de connexion Discord')
        return false
      }

      setUser(userData)
      localStorage.setItem('auth_user_id', userData.id)
      return true
    } catch (error) {
      setError('Erreur de connexion Discord')
      return false
    } finally {
      setLoading(false)
    }
  }

  const logout = () => {
    setUser(null)
    setError(null)
    localStorage.removeItem('auth_user_id')
  }

  const isAdmin = user?.role === 'admin'
  const isAuthenticated = !!user

  return {
    user,
    loading,
    error,
    login,
    loginDiscord,
    logout,
    isAdmin,
    isAuthenticated
  }
}

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const auth = useAuthProvider()

  return (
    <AuthContext.Provider value={auth}>
      {children}
    </AuthContext.Provider>
  )
}

export { AuthContext }
