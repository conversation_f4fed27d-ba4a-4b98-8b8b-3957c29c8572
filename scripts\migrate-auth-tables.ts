/**
 * Auth Tables Migration Script
 * Updates existing admin_users table and creates users table for authentication
 *
 * Usage: bun run scripts/migrate-auth-tables.ts
 */

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function migrateAuthTables() {
  console.log('🔄 Starting auth tables migration...')

  const migrations = [
    {
      name: 'Create users table',
      sql: `
        CREATE TABLE IF NOT EXISTS users (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          username VARCHAR(255) NOT NULL,
          email VARCHAR(255),
          discord_id VARCHAR(255) UNIQUE,
          avatar_url TEXT,
          role VARCHAR(50) DEFAULT 'user',
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          last_login TIMESTAMP WITH TIME ZONE
        );
      `
    },
    {
      name: 'Add password column to admin_users',
      sql: `
        ALTER TABLE admin_users 
        ADD COLUMN IF NOT EXISTS password VARCHAR(255);
      `
    },
    {
      name: 'Add email column to admin_users',
      sql: `
        ALTER TABLE admin_users 
        ADD COLUMN IF NOT EXISTS email VARCHAR(255);
      `
    },
    {
      name: 'Make discord_id nullable in admin_users',
      sql: `
        ALTER TABLE admin_users 
        ALTER COLUMN discord_id DROP NOT NULL;
      `
    },
    {
      name: 'Update username constraint in admin_users',
      sql: `
        ALTER TABLE admin_users 
        ADD CONSTRAINT admin_users_username_unique UNIQUE (username);
      `
    },
    {
      name: 'Insert default admin users',
      sql: `
        INSERT INTO admin_users (username, password, email, role, is_active) 
        VALUES 
          ('admin', 'admin123', '<EMAIL>', 'admin', true),
          ('taxhaa', 'royaume2024', '<EMAIL>', 'admin', true)
        ON CONFLICT (username) DO NOTHING;
      `
    }
  ]

  for (const migration of migrations) {
    try {
      console.log(`🔧 ${migration.name}...`)
      
      // Try direct SQL execution via fetch
      const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${supabaseServiceKey}`,
          'apikey': supabaseServiceKey,
        },
        body: JSON.stringify({ sql: migration.sql })
      })

      if (response.ok) {
        console.log(`✅ ${migration.name} - Success`)
      } else {
        console.log(`⚠️  ${migration.name} - May need manual execution`)
        console.log(`SQL: ${migration.sql.trim()}`)
      }
    } catch (error) {
      console.log(`⚠️  ${migration.name} - Will try alternative method`)
      
      // For the users table creation, try using Supabase client
      if (migration.name === 'Create users table') {
        try {
          // Try to query the table to see if it exists
          const { error } = await supabase.from('users').select('id').limit(1)
          if (error && error.message.includes('does not exist')) {
            console.log(`⚠️  Users table doesn't exist - needs manual creation`)
          } else {
            console.log(`✅ Users table already exists`)
          }
        } catch (e) {
          console.log(`⚠️  Users table status unknown`)
        }
      }
      
      // For admin users insertion, try using Supabase client
      if (migration.name === 'Insert default admin users') {
        try {
          const { error } = await supabase
            .from('admin_users')
            .upsert([
              {
                username: 'admin',
                password: 'admin123',
                email: '<EMAIL>',
                role: 'admin',
                is_active: true
              },
              {
                username: 'taxhaa',
                password: 'royaume2024',
                email: '<EMAIL>',
                role: 'admin',
                is_active: true
              }
            ], { onConflict: 'username' })

          if (!error) {
            console.log(`✅ Default admin users inserted via client`)
          } else {
            console.log(`⚠️  Admin users insertion failed: ${error.message}`)
          }
        } catch (e) {
          console.log(`⚠️  Admin users insertion failed via client`)
        }
      }
    }
  }

  console.log('')
  console.log('🎉 Auth tables migration completed!')
  console.log('')
  console.log('📋 If any migrations failed, please run these SQL commands manually in Supabase SQL Editor:')
  console.log('')
  
  for (const migration of migrations) {
    console.log(`-- ${migration.name}`)
    console.log(migration.sql.trim())
    console.log('')
  }
  
  console.log('🔍 Test your authentication:')
  console.log('1. Visit http://localhost:3000/test-auth')
  console.log('2. Try Discord OAuth login')
  console.log('3. Try admin login with: admin / admin123')
}

// Run the migration
migrateAuthTables().catch(console.error)
