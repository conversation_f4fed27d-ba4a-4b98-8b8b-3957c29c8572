import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const getSupabaseAdmin = () => {
  const url = process.env.NEXT_PUBLIC_SUPABASE_URL
  const key = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!url || !key) {
    throw new Error('Variables d\'environnement Supabase manquantes')
  }

  return createClient(url, key)
}

export async function POST(request: NextRequest) {
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json(
        { error: 'Configuration Supabase manquante' },
        { status: 500 }
      )
    }

    // SQL pour créer les tables
    const createTablesSQL = `
      -- Table pour les utilisateurs Discord
      CREATE TABLE IF NOT EXISTS users (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        username VARCHAR(255) NOT NULL,
        email VARCHAR(255),
        discord_id VARCHAR(255) UNIQUE,
        avatar_url TEXT,
        role VARCHAR(50) DEFAULT 'user',
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        last_login TIMESTAMP WITH TIME ZONE
      );

      -- Table pour les administrateurs
      CREATE TABLE IF NOT EXISTS admin_users (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        username VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        email VARCHAR(255),
        role VARCHAR(50) DEFAULT 'admin',
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        last_login TIMESTAMP WITH TIME ZONE
      );

      -- Insérer des admins par défaut
      INSERT INTO admin_users (username, password, email, role)
      VALUES
        ('admin', 'admin123', '<EMAIL>', 'admin'),
        ('taxhaa', 'royaume2024', '<EMAIL>', 'admin')
      ON CONFLICT (username) DO NOTHING;
    `

    // Essayer d'exécuter le SQL directement via l'API REST de Supabase
    try {
      const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${supabaseServiceKey}`,
          'apikey': supabaseServiceKey,
          'Prefer': 'return=minimal'
        },
        body: JSON.stringify({ sql: createTablesSQL })
      })

      if (response.ok) {
        return NextResponse.json({
          success: true,
          message: 'Tables créées et admins ajoutés avec succès'
        })
      }
    } catch (error) {
      console.log('Méthode RPC échouée, essai avec l\'API SQL directe...')
    }

    // Si la méthode RPC échoue, essayer avec l'endpoint SQL direct
    try {
      const response = await fetch(`${supabaseUrl}/rest/v1/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/sql',
          'Authorization': `Bearer ${supabaseServiceKey}`,
          'apikey': supabaseServiceKey,
          'Prefer': 'return=minimal'
        },
        body: createTablesSQL
      })

      if (response.ok) {
        return NextResponse.json({
          success: true,
          message: 'Tables créées et admins ajoutés avec succès'
        })
      }
    } catch (error) {
      console.log('Méthode SQL directe échouée, utilisation du client Supabase...')
    }

    // Fallback: utiliser le client Supabase pour créer les données
    const supabase = getSupabaseAdmin()

    // Essayer d'insérer directement dans admin_users (cela créera la table si elle n'existe pas)
    try {
      const { error: insertError } = await supabase
        .from('admin_users')
        .upsert([
          {
            username: 'admin',
            password: 'admin123',
            email: '<EMAIL>',
            role: 'admin',
            is_active: true
          },
          {
            username: 'taxhaa',
            password: 'royaume2024',
            email: '<EMAIL>',
            role: 'admin',
            is_active: true
          }
        ], {
          onConflict: 'username',
          ignoreDuplicates: true
        })

      if (!insertError) {
        return NextResponse.json({
          success: true,
          message: 'Tables créées et admins ajoutés avec succès'
        })
      } else {
        console.error('Erreur insertion:', insertError)
      }
    } catch (e) {
      console.error('Erreur client Supabase:', e)
    }

    // Si tout échoue, retourner les instructions SQL
    return NextResponse.json({
      success: false,
      needsManualCreation: true,
      message: 'Impossible de créer les tables automatiquement',
      sqlInstructions: createTablesSQL.trim(),
      error: 'Veuillez exécuter le SQL manuellement dans l\'éditeur Supabase'
    })

  } catch (error) {
    console.error('Erreur setup auth:', error)
    return NextResponse.json(
      { error: 'Erreur lors de la configuration' },
      { status: 500 }
    )
  }
}
